# 使用说明

## 快速开始

1. **启动应用**
   ```bash
   ./start.sh
   ```
   或者
   ```bash
   npm start
   ```

2. **测试功能**
   - 复制一些带格式的文本（比如从网页、Word文档等）
   - 在任何应用中按 `Cmd+B`
   - 文本将以纯文本形式粘贴，不带任何格式

## 功能演示

### 测试步骤
1. 打开一个网页，复制一些带有格式的文本（比如粗体、颜色、链接等）
2. 打开文本编辑器（如 TextEdit、Notes 等）
3. 按 `Cmd+B` - 文本将以纯文本形式粘贴
4. 对比：按 `Cmd+V` - 文本将保持原有格式

### 应用管理
- **显示设置窗口**: 点击系统托盘图标
- **隐藏窗口**: 按 `Escape` 或 `Cmd+W`
- **退出应用**: 右键点击托盘图标 → 退出

## 常见问题

### Q: 快捷键不工作？
A: 
- 确保应用正在运行（检查系统托盘）
- 检查是否有其他应用占用了 `Cmd+B` 快捷键
- 重启应用程序

### Q: 粘贴的内容不正确？
A: 
- 确保剪切板中有文本内容
- 检查目标应用是否支持文本粘贴
- 尝试手动 `Cmd+V` 确认剪切板内容

### Q: 如何更改快捷键？
A: 
- 编辑 `main.js` 文件
- 找到 `Command+B` 并修改为你想要的快捷键
- 重启应用

## 技术细节

### 工作原理
1. 应用注册全局快捷键 `Cmd+B`
2. 当快捷键触发时：
   - 读取系统剪切板中的文本内容
   - 清除剪切板中的格式信息
   - 将纯文本重新写入剪切板
   - 自动发送 `Cmd+V` 命令进行粘贴

### 支持的格式
- HTML 格式文本
- RTF (Rich Text Format)
- 带样式的文本
- 网页内容
- Word 文档内容
- 任何包含格式信息的文本

### 系统要求
- macOS 10.14 或更高版本
- 辅助功能权限（用于发送按键事件）

## 开发信息

### 项目结构
```
paste-plain-text/
├── main.js          # 主进程（快捷键、托盘、窗口管理）
├── index.html       # 用户界面
├── renderer.js      # 渲染进程（界面交互）
├── package.json     # 项目配置
├── start.sh         # 启动脚本
└── README.md        # 详细文档
```

### 自定义配置
可以在 `main.js` 中修改：
- 快捷键组合（第 89 行）
- 窗口大小和样式（第 10-25 行）
- 托盘菜单选项（第 60-80 行）

### 构建应用
```bash
# 构建 macOS 应用
npm run build

# 生成分发包
npm run dist
```
