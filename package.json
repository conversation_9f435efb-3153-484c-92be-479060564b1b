{"name": "paste-plain-text", "version": "1.0.0", "description": "Electron app for pasting plain text without formatting using Cmd+B", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "clipboard", "paste", "plain-text", "macos"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^36.3.2", "electron-builder": "^24.13.3"}, "build": {"appId": "com.yourname.paste-plain-text", "productName": "Paste Plain Text", "directories": {"output": "dist"}, "mac": {"category": "public.app-category.utilities", "target": "dmg"}}}