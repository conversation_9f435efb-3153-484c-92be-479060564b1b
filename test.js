#!/usr/bin/env node

/**
 * 测试脚本 - 验证 Paste Plain Text 应用功能
 */

const { clipboard } = require('electron');
const { spawn } = require('child_process');

console.log('🧪 Paste Plain Text 功能测试');
console.log('================================');

// 测试数据 - 包含各种格式的文本
const testData = [
  {
    name: '简单文本',
    text: 'Hello World',
    expected: 'Hello World'
  },
  {
    name: '带换行的文本',
    text: 'Line 1\nLine 2\nLine 3',
    expected: 'Line 1\nLine 2\nLine 3'
  },
  {
    name: '带特殊字符的文本',
    text: 'Test with émojis 🎉 and symbols ©®™',
    expected: 'Test with émojis 🎉 and symbols ©®™'
  },
  {
    name: '长文本',
    text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    expected: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'
  }
];

function testClipboard() {
  console.log('\n📋 测试剪切板功能...');
  
  testData.forEach((test, index) => {
    console.log(`\n${index + 1}. 测试: ${test.name}`);
    
    try {
      // 写入测试数据到剪切板
      clipboard.writeText(test.text);
      
      // 读取剪切板内容
      const clipboardContent = clipboard.readText();
      
      // 验证结果
      if (clipboardContent === test.expected) {
        console.log('   ✅ 通过');
      } else {
        console.log('   ❌ 失败');
        console.log(`   期望: "${test.expected}"`);
        console.log(`   实际: "${clipboardContent}"`);
      }
    } catch (error) {
      console.log(`   ❌ 错误: ${error.message}`);
    }
  });
}

function testHTMLContent() {
  console.log('\n🌐 测试 HTML 内容处理...');
  
  const htmlTests = [
    {
      name: 'HTML 标签',
      html: '<p><strong>Bold text</strong> and <em>italic text</em></p>',
      expectedText: 'Bold text and italic text'
    },
    {
      name: 'HTML 链接',
      html: '<a href="https://example.com">Click here</a>',
      expectedText: 'Click here'
    },
    {
      name: '复杂 HTML',
      html: '<div><h1>Title</h1><p>Paragraph with <span style="color: red;">colored text</span></p></div>',
      expectedText: 'Title\nParagraph with colored text'
    }
  ];
  
  htmlTests.forEach((test, index) => {
    console.log(`\n${index + 1}. 测试: ${test.name}`);
    
    try {
      // 模拟 HTML 内容（实际应用中会自动处理）
      clipboard.writeHTML(test.html);
      const textContent = clipboard.readText();
      
      console.log(`   HTML: ${test.html}`);
      console.log(`   文本: ${textContent}`);
      console.log('   ✅ HTML 内容已处理');
    } catch (error) {
      console.log(`   ❌ 错误: ${error.message}`);
    }
  });
}

function printSystemInfo() {
  console.log('\n💻 系统信息:');
  console.log(`   操作系统: ${process.platform}`);
  console.log(`   Node.js 版本: ${process.version}`);
  console.log(`   架构: ${process.arch}`);
}

function printUsageInstructions() {
  console.log('\n📖 使用说明:');
  console.log('1. 启动应用: npm start 或 ./start.sh');
  console.log('2. 复制带格式的文本（从网页、Word等）');
  console.log('3. 在任何应用中按 Cmd+B 粘贴纯文本');
  console.log('4. 对比: 按 Cmd+V 粘贴原格式文本');
  console.log('\n🎯 快捷键:');
  console.log('   Cmd+B  - 粘贴纯文本');
  console.log('   Escape - 隐藏设置窗口');
  console.log('   Cmd+W  - 隐藏设置窗口');
}

// 主测试函数
async function runTests() {
  try {
    printSystemInfo();
    
    // 检查是否在 Electron 环境中运行
    if (typeof require === 'undefined' || !require('electron')) {
      console.log('\n⚠️  警告: 此测试需要在 Electron 环境中运行');
      console.log('请使用以下命令启动应用后进行测试:');
      console.log('npm start');
      printUsageInstructions();
      return;
    }
    
    testClipboard();
    testHTMLContent();
    
    console.log('\n🎉 测试完成!');
    printUsageInstructions();
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('⚠️  此测试脚本需要在 Electron 应用中运行');
  console.log('请启动应用后在开发者控制台中运行测试');
  printUsageInstructions();
} else {
  // 导出测试函数供其他模块使用
  module.exports = {
    runTests,
    testClipboard,
    testHTMLContent
  };
}
