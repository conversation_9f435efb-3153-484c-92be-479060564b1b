#!/bin/bash

# Paste Plain Text 启动脚本
# 用于在 macOS 中启动纯文本粘贴应用

echo "🚀 启动 Paste Plain Text 应用..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

# 检查 npm 是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到 npm"
    exit 1
fi

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 启动应用
echo "✅ 启动应用..."
echo "💡 提示: 按 Cmd+B 可以在任何应用中粘贴纯文本"
echo "💡 提示: 应用将在后台运行，可通过系统托盘管理"
echo "💡 提示: 按 Ctrl+C 可以停止应用"
echo ""

npm start
