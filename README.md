# Paste Plain Text

一个简单的 Electron 应用程序，用于在 macOS 中粘贴纯文本内容（不带格式）。

## 功能特点

- 🔥 **全局快捷键**: 按 `Cmd+B` 在任何应用程序中粘贴纯文本
- 📋 **自动去格式**: 自动移除剪切板内容中的所有格式
- 🎯 **后台运行**: 应用在后台运行，不占用 Dock 空间
- 🔧 **系统托盘**: 通过系统托盘图标管理应用
- ⚡ **轻量级**: 基于 Electron，占用资源少

## 安装和运行

### 开发模式

1. 克隆或下载项目到本地
2. 安装依赖：
   ```bash
   npm install
   ```
3. 启动应用：
   ```bash
   npm start
   ```

### 开发调试模式

```bash
npm run dev
```

### 构建应用

构建 macOS 应用程序：
```bash
npm run build
```

生成分发包：
```bash
npm run dist
```

## 使用方法

1. **启动应用**: 运行应用后，它会在后台运行
2. **复制内容**: 在任何应用中复制带格式的文本（如网页、Word文档等）
3. **粘贴纯文本**: 按 `Cmd+B` 即可在当前应用中粘贴纯文本版本
4. **管理应用**: 点击系统托盘图标可以显示/隐藏设置窗口或退出应用

## 快捷键

- `Cmd+B`: 粘贴纯文本（主要功能）
- `Escape`: 隐藏设置窗口
- `Cmd+W`: 隐藏设置窗口

## 系统要求

- macOS 10.14 或更高版本
- Node.js 16 或更高版本（仅开发时需要）

## 工作原理

1. 应用注册全局快捷键 `Cmd+B`
2. 当快捷键被触发时：
   - 读取系统剪切板中的文本内容
   - 清除剪切板中的格式信息
   - 将纯文本重新写入剪切板
   - 自动发送 `Cmd+V` 命令进行粘贴

## 项目结构

```
paste-plain-text/
├── main.js          # Electron 主进程
├── index.html       # 应用界面
├── renderer.js      # 渲染进程脚本
├── package.json     # 项目配置
├── assets/          # 资源文件（图标等）
└── README.md        # 说明文档
```

## 故障排除

### 快捷键不工作
- 确保应用正在运行（检查系统托盘）
- 检查是否有其他应用占用了 `Cmd+B` 快捷键
- 重启应用程序

### 粘贴不工作
- 确保剪切板中有文本内容
- 检查目标应用是否支持文本粘贴
- 尝试手动 `Cmd+V` 确认剪切板内容正确

### 应用无法启动
- 检查 Node.js 版本是否符合要求
- 重新安装依赖：`npm install`
- 查看控制台错误信息

## 开发说明

### 主要文件说明

- **main.js**: 主进程，负责窗口管理、全局快捷键注册、系统托盘
- **index.html**: 用户界面，提供设置和状态显示
- **renderer.js**: 渲染进程，处理界面交互和剪切板操作

### 自定义配置

可以在 `main.js` 中修改：
- 快捷键组合（默认 `Command+B`）
- 窗口大小和样式
- 托盘菜单选项

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 初始版本
- 支持 Cmd+B 粘贴纯文本
- 系统托盘支持
- macOS 优化
